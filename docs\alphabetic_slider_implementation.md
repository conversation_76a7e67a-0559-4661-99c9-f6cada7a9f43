# Alphabetic Slider Implementation

This document describes the implementation of the alphabetic slider in the contacts list using the `azlistview` package.

## Overview

The contacts list now features an alphabetic slider that allows users to quickly navigate through contacts by jumping to specific letters. The implementation uses the `azlistview` package to provide smooth scrolling and intuitive navigation.

## Key Components

### 1. ContactAzItem Model (`lib/models/contact_az_item.dart`)

A wrapper class that extends `ISuspensionBean` to make `ContactModel` compatible with `AzListView`:

```dart
class ContactAzItem extends ISuspensionBean {
  final ContactModel contactModel;
  final String tag;
  
  @override
  String getSuspensionTag() => tag;
}
```

**Key Features:**
- Automatically generates alphabetic tags (A-Z, #)
- Handles special characters and numbers (grouped under '#')
- Provides sorting and suspension status management
- Extracts unique alphabetic tags for the index bar

### 2. Enhanced ContactListView

The `ContactListView` has been updated to use `AzListView` instead of regular `ListView.builder`:

**Features:**
- **Alphabetic Index Bar**: Right-side slider with A-Z letters
- **Suspension Headers**: Sticky headers showing current letter section
- **Smooth Navigation**: Tap any letter to jump to that section
- **Visual Feedback**: Highlighted letters and hint popup during navigation
- **Pull-to-Refresh**: Maintained refresh functionality

## Implementation Details

### Alphabetic Grouping Logic

```dart
String firstChar = contactModel.displayName.isNotEmpty 
    ? contactModel.displayName[0].toUpperCase() 
    : '#';

// Handle special characters and numbers
if (!RegExp(r'^[A-Z]$').hasMatch(firstChar)) {
  firstChar = '#';
}
```

- Contacts starting with A-Z are grouped by their first letter
- Contacts starting with numbers or special characters are grouped under '#'
- Empty display names default to '#'

### Index Bar Configuration

```dart
indexBarOptions: IndexBarOptions(
  needRebuild: true,
  ignoreDragCancel: true,
  downTextStyle: TextStyle(fontSize: 12, color: primary),
  downItemDecoration: BoxDecoration(
    shape: BoxShape.circle,
    color: primary.withOpacity(0.8),
  ),
  indexHintDecoration: BoxDecoration(
    color: primary.withOpacity(0.8),
    borderRadius: BorderRadius.circular(4.0),
  ),
  // ... more styling options
)
```

### Suspension Headers

Sticky headers appear at the top of each alphabetic section:

```dart
Widget _buildSuspensionWidget(String tag, BuildContext context) {
  return Container(
    height: 40,
    padding: const EdgeInsets.only(left: 16.0),
    color: surface.withOpacity(0.8),
    child: Text(tag, style: headerStyle),
  );
}
```

## User Experience

### Navigation Methods

1. **Scroll Normally**: Traditional scrolling through the list
2. **Tap Index Bar**: Tap any letter to jump to that section
3. **Drag Index Bar**: Drag finger along the index bar for quick navigation
4. **Pull to Refresh**: Pull down to refresh the contact list

### Visual Feedback

- **Active Letter Highlighting**: Currently visible section is highlighted
- **Navigation Hint**: Large letter popup appears during index bar interaction
- **Smooth Transitions**: Animated scrolling between sections
- **Consistent Theming**: Uses app's color scheme throughout

## Favorite Contacts Integration

Favorite contacts are still displayed at the top of the list, before the alphabetic sections:

- Shown with "Favourite" header
- Not included in alphabetic grouping
- Maintained existing functionality

## Performance Considerations

- **Efficient Sorting**: Contacts are sorted once during conversion to `ContactAzItem`
- **Lazy Loading**: Only visible items are rendered
- **Optimized Rebuilds**: Index bar rebuilds only when necessary
- **Memory Efficient**: Minimal overhead for wrapper objects

## Testing

Basic tests are included in `test/cubit/contacts_cubit_test.dart`:

- Alphabetic tag generation
- Special character handling
- Tag extraction and sorting

## Platform Compatibility

- **iOS**: Full support with native-like index bar behavior
- **Android**: Full support with Material Design styling
- **Desktop**: Functional but optimized for touch interfaces

## Future Enhancements

Potential improvements:

1. **Search Integration**: Filter index bar based on search results
2. **Custom Grouping**: Allow grouping by other criteria (company, location)
3. **Accessibility**: Enhanced screen reader support
4. **Animations**: More sophisticated transition animations
5. **Customization**: User-configurable index bar position and styling

## Dependencies

- `azlistview: ^2.0.0` - Core alphabetic list functionality
- Existing app theming and styling systems
- Flutter's built-in `RefreshIndicator` for pull-to-refresh
