# Audio Device Implementation Test Plan

## Overview
This document outlines the testing approach for the new audio device selection system that replaces the simple speaker toggle functionality.

## Implementation Summary
- ✅ Created AudioDevice model and enum with device type classification
- ✅ Created AudioDeviceState for state management
- ✅ Created AudioDeviceService for low-level device operations
- ✅ Created AudioDeviceCubit for business logic and state management
- ✅ Updated service locator to register new services
- ✅ Removed isPhoneSpeakerOn from HomeState and updated HomeCubit
- ✅ Created AudioDeviceSelector widget for UI
- ✅ Updated AcceptedCallDialog and OutgoingCallDialog to use new widget
- ✅ Registered AudioDeviceCubit in global bloc providers
- ✅ Added dedicated AudioDeviceType.none for desktop speaker mute functionality

## Manual Testing Checklist

### 1. Compilation Test
- [x] All files compile without errors
- [x] No missing imports or dependencies
- [x] Service locator properly configured

### 2. Basic Functionality Tests

#### Mobile Platform Tests
- [ ] **Incoming Call Audio Device Selection**
  1. Receive an incoming call
  2. Accept the call
  3. Verify AudioDeviceSelector appears in AcceptedCallDialog
  4. Test device selection (if multiple devices available)
  5. Verify audio switches correctly

- [ ] **Outgoing Call Audio Device Selection**
  1. Make an outgoing call
  2. Verify AudioDeviceSelector appears in OutgoingCallDialog
  3. Test device selection during call
  4. Verify audio switches correctly

#### Desktop Platform Tests
- [ ] **Desktop Audio Device Selection**
  1. Make or receive a call on desktop
  2. Click on AudioDeviceSelector
  3. Verify dropdown/dialog shows available devices
  4. Select different audio devices
  5. Verify audio output changes

### 3. Device Priority Tests (Mobile)
- [ ] **Device Priority Order**
  1. Connect wired headphones → should auto-select
  2. Connect Bluetooth headphones → should auto-select (higher priority than speaker)
  3. Connect Bluetooth speaker → should auto-select (lower priority than headphones)
  4. Disconnect devices → should fall back to earpiece/speaker

### 4. Backward Compatibility Tests
- [ ] **HomeCubit.toggleSpeakerPhone()**
  1. Call the legacy method
  2. Verify it delegates to AudioDeviceCubit
  3. Verify no compilation errors in existing code

### 5. Error Handling Tests
- [ ] **Service Errors**
  1. Simulate WebRTC device enumeration failure
  2. Verify error state is handled gracefully
  3. Verify UI shows appropriate error indication

- [ ] **Device Selection Errors**
  1. Attempt to select unavailable device
  2. Verify error handling
  3. Verify fallback behavior

### 6. Performance Tests
- [ ] **Device Monitoring**
  1. Connect/disconnect audio devices during call
  2. Verify device list updates within 2-3 seconds
  3. Verify no memory leaks from polling timer

### 7. Platform-Specific Tests

#### iOS Specific
- [ ] Test with AirPods
- [ ] Test with wired headphones
- [ ] Test device switching during call
- [ ] Verify device IDs match iOS conventions

#### Android Specific
- [ ] Test with Bluetooth headphones
- [ ] Test with wired headphones
- [ ] Test with Bluetooth speakers
- [ ] Verify device IDs match Android conventions

#### Windows/macOS Specific
- [ ] Test with multiple audio output devices
- [ ] Test "None" option to turn off speaker
- [ ] Test system default device selection

## Expected Behavior

### Mobile (iOS/Android)
- Simple icon showing current device type
- Tap to cycle through available devices or show selection dialog
- Automatic device selection based on priority when new devices connect

### Desktop (Windows/macOS)
- Icon with dropdown arrow
- Click to show device selection dialog
- Manual device selection with "None" option available

## Validation Criteria

### ✅ Success Criteria
1. All compilation errors resolved
2. UI components properly integrated
3. Audio device selection works on all platforms
4. Backward compatibility maintained
5. No performance regressions
6. Device monitoring works correctly

### ❌ Failure Indicators
1. Compilation errors
2. Runtime exceptions
3. Audio not switching when device selected
4. UI not updating when devices change
5. Memory leaks from device monitoring
6. Existing functionality broken

## Notes
- The implementation uses WebRTC's Helper.audiooutputs and Helper.selectAudioOutput
- Device change detection uses polling (2-second interval) since WebRTC doesn't provide native events
- Mobile device priority: Wired Headphones > Bluetooth Headphones > Bluetooth Speakers > Other Bluetooth > Earpiece > Loudspeaker
- Desktop includes special "None" option to turn off speaker output

## Next Steps After Testing
1. Address any issues found during manual testing
2. Consider adding automated tests if bloc_test dependency is added
3. Optimize device polling interval if needed
4. Add user preferences for default device selection
5. Consider adding haptic feedback for device switching on mobile
