# Phone Book Contacts Integration

This document describes the integration of device phone book contacts with the DDOne app using the `flutter_contacts` package.

## Overview

The app now automatically fetches contacts from the user's device phone book and displays them alongside locally created contacts and contacts from the fusion API. All contacts are treated uniformly using the `ContactModel` structure.

## Implementation Details

### Key Components

1. **ContactsCubit** - Enhanced to fetch phone book contacts
2. **PermissionUtil** - <PERSON><PERSON> contacts permission requests
3. **ContactsScreen** - Requests permission before fetching contacts

### Contact Sources

The app now supports three types of contacts:

1. **Fusion API Contacts** - Contacts from the server API
2. **Phone Book Contacts** - Contacts from device phone book (NEW)
3. **Local Contacts** - Contacts created within the app

### Permission Handling

- Contacts permission is requested using `PermissionUtil.handleCustomBoolPermissionRequest` with `FlutterContacts.requestPermission()`
- Permission is requested when the contacts screen is initialized
- Permission is also requested during refresh operations
- Desktop platforms skip permission requests as they don't need contacts access
- Uses `FlutterContacts.requestPermission(readonly: true)` to check current permission status

### Phone Number Cleaning

Phone numbers from the device phone book are cleaned to ensure consistency:
- Removes spaces, dashes, parentheses, and other formatting characters
- Preserves the '+' character at the beginning for international numbers
- Removes any additional '+' characters that might appear elsewhere

### Contact Model Structure

Phone book contacts are converted to `ContactModel` format:

```dart
ContactModel(
  contactEntries: [ContactEntry(
    entryId: '${contact.id}_${phone.label.name}',
    label: phone.label.name,
    type: 'phone',
    uri: cleanedPhoneNumber,
  )],
  contactId: cleanedPhoneNumber,
  displayName: displayName, // Includes label for multiple numbers
  isLocalContact: true, // Phone book contacts are treated as local contacts
)
```

### Multiple Phone Numbers Handling

For contacts with multiple phone numbers:
- Each phone number creates a separate `ContactModel` entry
- Display names include the label: `"ContactName (label)"` (e.g., "LohZJ (work)", "LohZJ (mobile)")
- Contacts with single numbers use the contact name without label
- Each entry gets a unique `entryId` combining contact ID and label

## Usage

### For Users

1. When opening the contacts screen, the app will request permission to access contacts
2. If permission is granted, phone book contacts will automatically appear in the contact list
3. Phone book contacts are sorted alphabetically with other contacts
4. Phone book contacts can be used for calling just like other contacts

### For Developers

The integration is automatic. Key methods:

- `ContactsCubit.requestContactsPermission(context)` - Request permission using PermissionUtil with FlutterContacts
- `ContactsCubit._getPhoneBookContacts()` - Fetch and convert phone book contacts (creates separate entries for multiple numbers)
- `ContactsCubit._cleanPhoneNumber(phoneNumber)` - Clean phone number formatting

## Platform Support

- **iOS**: Supported with `NSContactsUsageDescription` permission
- **Android**: Supported with `READ_CONTACTS` permission  
- **Desktop**: Skipped (not applicable)

## Error Handling

- Permission denied: Contacts are skipped, no error shown to user
- No phone numbers: Contacts without phone numbers are filtered out
- API errors: Logged but don't prevent other contact sources from loading

## Testing

Basic test structure is provided in `test/cubit/contacts_cubit_test.dart`. For comprehensive testing, mock the following dependencies:

- `FlutterContacts.getContacts()`
- `FlutterContacts.requestPermission()`
- `HiveService`
- `ContactRepository`

## Future Enhancements

Potential improvements:

1. ✅ Support multiple phone numbers per contact (IMPLEMENTED)
2. Include email addresses from contacts
3. Contact photos/avatars
4. Contact synchronization and caching
5. Search within phone book contacts specifically
6. Merge duplicate contacts with same phone numbers
7. Contact grouping by source (phone book vs local vs API)
